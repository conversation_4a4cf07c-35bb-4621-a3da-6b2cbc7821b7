{"name": "fanzd-schedule-service", "version": "1.0.0", "description": "樊振东球迷网站赛程服务", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "sync": "node src/sync/index.js"}, "keywords": ["fanzhendong", "schedule-service", "api", "express", "sports"], "author": "FanZD Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "mysql2": "^3.6.5", "redis": "^4.6.10", "axios": "^1.6.2", "node-cron": "^3.0.3", "moment-timezone": "^0.5.43", "express-rate-limit": "^7.1.5", "joi": "^17.11.0", "winston": "^3.11.0", "uuid": "^9.0.1", "socket.io": "^4.7.4", "ical-generator": "^4.1.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0"}, "engines": {"node": ">=18.0.0"}}