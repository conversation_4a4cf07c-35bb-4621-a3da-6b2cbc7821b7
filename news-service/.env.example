# 服务器配置
NODE_ENV=development
PORT=27005
HOST=localhost



# 新闻爬虫配置
CRAWLER_ENABLED=true
CRAWLER_INTERVAL=3600000
NEWS_SOURCES=sina,163,sohu,tencent
MAX_NEWS_PER_SOURCE=50

# 外部API配置
NEWS_API_KEY=your-news-api-key
SPORTS_API_KEY=your-sports-api-key
WEIBO_API_KEY=your-weibo-api-key

# 内容处理配置
AUTO_TRANSLATE=false
CONTENT_FILTER=true
DUPLICATE_CHECK=true
CACHE_TTL=1800

# 推送配置
PUSH_ENABLED=true
WEBHOOK_URL=http://localhost:27001/api/webhooks/news
NOTIFICATION_TOPICS=breaking,match,interview

# 安全配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/news-service.log

# CORS配置
CORS_ORIGIN=http://localhost:3000
CORS_CREDENTIALS=true

# 服务间通信
API_SERVICE_URL=http://localhost:27001
USER_SERVICE_URL=http://localhost:27003
MEDIA_SERVICE_URL=http://localhost:27004
