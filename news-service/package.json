{"name": "fanzd-news-service", "version": "1.0.0", "description": "樊振东球迷网站新闻服务", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "crawler": "node src/crawler/index.js"}, "keywords": ["fanzhendong", "news-service", "api", "express", "crawler"], "author": "FanZD Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "mysql2": "^3.6.5", "redis": "^4.6.10", "axios": "^1.6.2", "cheerio": "^1.0.0-rc.12", "puppeteer": "^21.6.1", "node-cron": "^3.0.3", "express-rate-limit": "^7.1.5", "joi": "^17.11.0", "winston": "^3.11.0", "uuid": "^9.0.1", "moment": "^2.29.4", "natural": "^6.7.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0"}, "engines": {"node": ">=18.0.0"}}