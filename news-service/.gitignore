# 依赖目录
node_modules/
__pycache__/
*.py[cod]
*$py.class
pip-log.txt
pip-delete-this-directory.txt

# Java相关
*.class
*.jar
*.war
*.ear
target/
.gradle/
build/
out/

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志文件
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 编辑器和IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# 缓存目录
.cache/
.pytest_cache/
.coverage
.nyc_output/

# 临时文件
tmp/
temp/
*.tmp
*.temp

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 新闻服务特定
# 新闻图片和媒体文件
news-images/
thumbnails/
media-cache/
cdn-cache/

# 新闻内容缓存
article-cache/
rss-cache/
feed-cache/

# 爬虫相关
scraped-data/
crawl-logs/
spider-cache/

# 搜索索引
search-index/
elasticsearch-data/
solr-data/

# RSS和Feed文件
feeds/generated/
rss/output/

# 内容分析和NLP
nlp-models/
sentiment-data/
keyword-cache/

# 新闻来源配置
sources/local.*
feeds/local.*

# Docker相关
.dockerignore
Dockerfile.local

# SSL证书和密钥文件
*.pem
*.key
*.crt
*.p12
ssl/
certs/

# 配置文件
config/local.*
config/production.*
config/development.*

# 测试相关
test-data/
mock-news/
fixtures/

# 备份文件
*.bak
*.backup
*.old 