# 樊振东球迷网站AI应援物生成器 - 部署指南

## 📋 目录

1. [环境要求](#环境要求)
2. [开发环境部署](#开发环境部署)
3. [生产环境部署](#生产环境部署)
4. [Docker部署](#docker部署)
5. [配置说明](#配置说明)
6. [故障排除](#故障排除)

## 🔧 环境要求

### 系统要求
- **操作系统**: Linux (Ubuntu 20.04+) / macOS / Windows 10+
- **内存**: 最低4GB，推荐8GB+
- **存储**: 最低20GB可用空间
- **网络**: 稳定的互联网连接（用于AI API调用）

### 软件依赖
- **Node.js**: 18.0+ (推荐18.19.0)
- **Python**: 3.9+ (推荐3.11.0)
- **MySQL**: 8.0+
- **Redis**: 6.0+
- **Nginx**: 1.18+ (生产环境)

### 外部服务
- **Google Gemini API**: 需要有效的API密钥
- **域名和SSL证书**: 生产环境需要

## 🚀 开发环境部署

### 1. 克隆项目
```bash
git clone https://github.com/your-repo/fanzdnet-ai-generator.git
cd fanzdnet-ai-generator
```

### 2. 数据库配置

#### 安装MySQL
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install mysql-server

# macOS (使用Homebrew)
brew install mysql

# 启动MySQL服务
sudo systemctl start mysql  # Linux
brew services start mysql   # macOS
```

#### 创建数据库
```bash
mysql -u root -p
```

```sql
CREATE DATABASE fanzd_net CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'fanzdnet'@'localhost' IDENTIFIED BY 'your-secure-password';
GRANT ALL PRIVILEGES ON fanzd_net.* TO 'fanzdnet'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

#### 初始化数据库表
```bash
mysql -u fanzdnet -p fanzd_net < database/init_fanzd_net.sql
```

### 3. Redis配置

#### 安装Redis
```bash
# Ubuntu/Debian
sudo apt install redis-server

# macOS (使用Homebrew)
brew install redis

# 启动Redis服务
sudo systemctl start redis-server  # Linux
brew services start redis          # macOS
```

#### 验证Redis
```bash
redis-cli ping
# 应该返回: PONG
```

### 4. 后端API服务部署

```bash
cd backend/api-service

# 安装依赖
npm install

# 复制环境配置
cp .env.example .env

# 编辑配置文件
nano .env
```

**配置.env文件**:
```env
# 服务配置
NODE_ENV=development
PORT=3001
HOST=localhost

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=fanzdnet
DB_PASSWORD=your-secure-password
DB_NAME=fanzd_net

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# 文件存储配置
STORAGE_PATH=../../storage
UPLOAD_PATH=../../storage/uploads
MAX_FILE_SIZE=10485760

# 日志配置
LOG_LEVEL=info
```

#### 启动API服务
```bash
# 开发模式
npm run dev

# 生产模式
npm start
```

### 5. Python AI服务部署

```bash
cd backend/ai-service

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate  # Linux/macOS
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt

# 复制环境配置
cp .env.example .env

# 编辑配置文件
nano .env
```

**配置.env文件**:
```env
# 服务配置
HOST=0.0.0.0
PORT=8000
DEBUG=True
LOG_LEVEL=INFO

# AI服务配置
GEMINI_API_KEY=your-gemini-api-key-here
AI_REQUEST_TIMEOUT=300
MAX_CONCURRENT_REQUESTS=10

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=1

# MySQL配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=fanzdnet
DB_PASSWORD=your-secure-password
DB_NAME=fanzd_net

# 文件存储配置
STORAGE_PATH=../../storage
GENERATED_PATH=../../storage/generated
TEMP_PATH=../../storage/temp
```

#### 启动AI服务
```bash
# 开发模式
python -m app.main

# 或使用uvicorn
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### 6. 前端应用部署

```bash
cd frontpage/ai-generator

# 安装依赖
npm install

# 复制环境配置
cp .env.example .env

# 编辑配置文件
nano .env
```

**配置.env文件**:
```env
# API服务地址
VITE_API_BASE_URL=http://localhost:3001/api
VITE_AI_API_BASE_URL=http://localhost:8000/api/v1

# 应用配置
VITE_APP_TITLE=樊振东球迷网站AI应援物生成器
VITE_APP_VERSION=1.0.0
```

#### 启动前端服务
```bash
# 开发模式
npm run dev

# 构建生产版本
npm run build
```

### 7. 创建存储目录
```bash
mkdir -p storage/{uploads,generated,temp,thumbnails}
chmod 755 storage
chmod 755 storage/*
```

### 8. 验证部署
1. **前端应用**: http://localhost:3000
2. **API服务**: http://localhost:3001/health
3. **AI服务**: http://localhost:8000/health

## 🏭 生产环境部署

### 1. 服务器准备

#### 系统更新
```bash
sudo apt update && sudo apt upgrade -y
```

#### 安装必要软件
```bash
sudo apt install -y curl wget git nginx certbot python3-certbot-nginx
```

#### 安装Node.js
```bash
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

#### 安装Python
```bash
sudo apt install -y python3 python3-pip python3-venv
```

### 2. 数据库配置（生产环境）

#### MySQL安全配置
```bash
sudo mysql_secure_installation
```

#### 创建生产数据库
```sql
CREATE DATABASE fanzd_net_prod CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'fanzdnet_prod'@'localhost' IDENTIFIED BY 'very-secure-production-password';
GRANT ALL PRIVILEGES ON fanzd_net_prod.* TO 'fanzdnet_prod'@'localhost';
FLUSH PRIVILEGES;
```

### 3. 应用部署

#### 创建应用目录
```bash
sudo mkdir -p /var/www/fanzdnet
sudo chown $USER:$USER /var/www/fanzdnet
cd /var/www/fanzdnet
```

#### 克隆代码
```bash
git clone https://github.com/your-repo/fanzdnet-ai-generator.git .
```

#### 配置生产环境变量
```bash
# API服务配置
cp backend/api-service/.env.example backend/api-service/.env.production

# AI服务配置
cp backend/ai-service/.env.example backend/ai-service/.env.production

# 编辑生产配置文件
nano backend/api-service/.env.production
nano backend/ai-service/.env.production
```

#### 安装依赖和构建
```bash
# API服务
cd backend/api-service
npm ci --only=production
cd ../..

# AI服务
cd backend/ai-service
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
cd ../..

# 前端应用
cd frontpage/ai-generator
npm ci
npm run build
cd ../..
```

### 4. 进程管理（PM2）

#### 安装PM2
```bash
sudo npm install -g pm2
```

#### 创建PM2配置文件
```bash
nano ecosystem.config.js
```

```javascript
module.exports = {
  apps: [
    {
      name: 'fanzdnet-api',
      script: 'backend/api-service/src/app.js',
      cwd: '/var/www/fanzdnet',
      env: {
        NODE_ENV: 'production',
        PORT: 3001
      },
      instances: 2,
      exec_mode: 'cluster',
      max_memory_restart: '1G',
      error_file: '/var/log/fanzdnet/api-error.log',
      out_file: '/var/log/fanzdnet/api-out.log',
      log_file: '/var/log/fanzdnet/api-combined.log'
    },
    {
      name: 'fanzdnet-ai',
      script: 'backend/ai-service/venv/bin/python',
      args: '-m app.main',
      cwd: '/var/www/fanzdnet/backend/ai-service',
      env: {
        PYTHONPATH: '/var/www/fanzdnet/backend/ai-service'
      },
      instances: 1,
      max_memory_restart: '2G',
      error_file: '/var/log/fanzdnet/ai-error.log',
      out_file: '/var/log/fanzdnet/ai-out.log',
      log_file: '/var/log/fanzdnet/ai-combined.log'
    }
  ]
};
```

#### 启动应用
```bash
# 创建日志目录
sudo mkdir -p /var/log/fanzdnet
sudo chown $USER:$USER /var/log/fanzdnet

# 启动应用
pm2 start ecosystem.config.js

# 保存PM2配置
pm2 save

# 设置开机自启
pm2 startup
```

### 5. Nginx配置

#### 创建Nginx配置文件
```bash
sudo nano /etc/nginx/sites-available/fanzdnet
```

```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;
    
    # SSL配置
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # 前端静态文件
    location / {
        root /var/www/fanzdnet/frontpage/ai-generator/dist;
        try_files $uri $uri/ /index.html;
        
        # 缓存配置
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API代理
    location /api/ {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 超时配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # AI服务代理
    location /ai-api/ {
        rewrite ^/ai-api/(.*) /api/v1/$1 break;
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 长时间请求支持（AI生成）
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }
    
    # 文件上传大小限制
    client_max_body_size 10M;
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
}
```

#### 启用站点
```bash
sudo ln -s /etc/nginx/sites-available/fanzdnet /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 6. SSL证书配置

#### 获取Let's Encrypt证书
```bash
sudo certbot --nginx -d your-domain.com -d www.your-domain.com
```

#### 设置自动续期
```bash
sudo crontab -e
```

添加以下行：
```
0 12 * * * /usr/bin/certbot renew --quiet
```

### 7. 防火墙配置

```bash
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw enable
```

### 8. 监控和日志

#### 设置日志轮转
```bash
sudo nano /etc/logrotate.d/fanzdnet
```

```
/var/log/fanzdnet/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        pm2 reload all
    endscript
}
```

## 🐳 Docker部署

### 1. 创建Dockerfile

#### API服务Dockerfile
```dockerfile
# backend/api-service/Dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .

EXPOSE 3001

CMD ["npm", "start"]
```

#### AI服务Dockerfile
```dockerfile
# backend/ai-service/Dockerfile
FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["python", "-m", "app.main"]
```

### 2. 创建docker-compose.yml

```yaml
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: fanzd_net
      MYSQL_USER: fanzdnet
      MYSQL_PASSWORD: password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init_fanzd_net.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "3306:3306"

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"

  api-service:
    build: ./backend/api-service
    environment:
      NODE_ENV: production
      DB_HOST: mysql
      REDIS_HOST: redis
    ports:
      - "3001:3001"
    depends_on:
      - mysql
      - redis
    volumes:
      - ./storage:/app/storage

  ai-service:
    build: ./backend/ai-service
    environment:
      DB_HOST: mysql
      REDIS_HOST: redis
      GEMINI_API_KEY: ${GEMINI_API_KEY}
    ports:
      - "8000:8000"
    depends_on:
      - mysql
      - redis
    volumes:
      - ./storage:/app/storage

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./frontpage/ai-generator/dist:/usr/share/nginx/html
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - api-service
      - ai-service

volumes:
  mysql_data:
```

### 3. 启动Docker服务

```bash
# 构建和启动
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

## ⚠️ 故障排除

### 常见问题

#### 1. 数据库连接失败
```bash
# 检查MySQL服务状态
sudo systemctl status mysql

# 检查端口占用
netstat -tlnp | grep 3306

# 测试连接
mysql -h localhost -u fanzdnet -p fanzd_net
```

#### 2. Redis连接失败
```bash
# 检查Redis服务状态
sudo systemctl status redis-server

# 测试连接
redis-cli ping
```

#### 3. AI服务启动失败
```bash
# 检查Python环境
python3 --version
pip list

# 检查Gemini API密钥
echo $GEMINI_API_KEY

# 查看详细错误日志
tail -f /var/log/fanzdnet/ai-error.log
```

#### 4. 前端构建失败
```bash
# 清理缓存
npm cache clean --force

# 重新安装依赖
rm -rf node_modules package-lock.json
npm install

# 检查Node.js版本
node --version
npm --version
```

#### 5. Nginx配置错误
```bash
# 测试配置文件
sudo nginx -t

# 查看错误日志
sudo tail -f /var/log/nginx/error.log

# 重新加载配置
sudo systemctl reload nginx
```

### 性能优化

#### 1. 数据库优化
```sql
-- 添加索引
ALTER TABLE fazd_generation_records ADD INDEX idx_user_status (user_id, status);
ALTER TABLE fazd_file_records ADD INDEX idx_user_type (user_id, file_type);

-- 优化配置
SET GLOBAL innodb_buffer_pool_size = 1073741824; -- 1GB
```

#### 2. Redis优化
```bash
# 编辑Redis配置
sudo nano /etc/redis/redis.conf

# 设置最大内存
maxmemory 512mb
maxmemory-policy allkeys-lru
```

#### 3. Node.js优化
```bash
# 设置环境变量
export NODE_OPTIONS="--max-old-space-size=2048"
```

---

**部署支持**: <EMAIL>  
**更新时间**: 2024年12月  
**版本**: v1.0.0
