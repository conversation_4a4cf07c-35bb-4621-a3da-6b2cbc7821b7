# 樊振东球迷网站 - 端口配置说明

## 🌐 **端口分配方案**

### **主要服务端口**
| 服务名称 | 端口号 | 描述 | 状态 |
|---------|--------|------|------|
| 前端应用 | 3000 | Vue.js主网站 | ✅ 已配置 |
| API服务 | 27001 | 主API服务 | ✅ 已配置 |
| AI服务 | 27002 | AI应援物生成服务 | ✅ 已配置 |
| 用户服务 | 27003 | 用户管理服务 | ✅ 已配置 |
| 媒体服务 | 27004 | 媒体文件处理服务 | ✅ 已配置 |
| 新闻服务 | 27005 | 新闻爬虫和管理服务 | ✅ 已配置 |
| 赛程服务 | 27006 | 比赛赛程管理服务 | ✅ 已配置 |
| API网关 | 27007 | 统一API网关 | ✅ 已配置 |

### **监控和辅助端口**
| 服务名称 | 端口号 | 描述 | 状态 |
|---------|--------|------|------|
| AI监控 | 27008 | AI服务监控指标 | ✅ 已配置 |
| 网关监控 | 27009 | API网关监控指标 | ✅ 已配置 |

## 📁 **配置文件位置**

### **后端服务配置**
```
backend/
├── api-service/
│   ├── .env                    # PORT=27001
│   └── .env.example           # PORT=27001
├── ai-service/
│   ├── .env                    # PORT=27002, METRICS_PORT=27008
│   └── .env.example           # PORT=27002, METRICS_PORT=27008
user-service/
├── .env.example               # PORT=27003
└── package.json               # 服务配置
media-service/
├── .env.example               # PORT=27004
└── package.json               # 服务配置
news-service/
├── .env.example               # PORT=27005
└── package.json               # 服务配置
schedule-service/
├── .env.example               # PORT=27006
└── package.json               # 服务配置
api-gateway/
├── .env.example               # PORT=27007, METRICS_PORT=27009
└── package.json               # 服务配置
```

### **前端配置**
```
frontpage/
├── .env                       # API地址配置
├── .env.example              # API地址配置
├── vite.config.js            # 代理配置
└── ai-generator/
    ├── .env                   # 独立AI生成器配置
    ├── .env.example          # 独立AI生成器配置
    └── vite.config.js        # 代理配置
```

## 🔧 **服务间通信配置**

### **API网关路由配置**
```env
# api-gateway/.env
API_SERVICE_URL=http://localhost:27001
AI_SERVICE_URL=http://localhost:27002
USER_SERVICE_URL=http://localhost:27003
MEDIA_SERVICE_URL=http://localhost:27004
NEWS_SERVICE_URL=http://localhost:27005
SCHEDULE_SERVICE_URL=http://localhost:27006
```

### **前端代理配置**
```javascript
// frontpage/vite.config.js
proxy: {
  '/api': {
    target: 'http://localhost:27007',  // 通过API网关
    changeOrigin: true
  },
  '/api/auth': {
    target: 'http://localhost:27001',  // 直连API服务
    changeOrigin: true
  },
  '/api/ai': {
    target: 'http://localhost:27002',  // 直连AI服务
    changeOrigin: true
  }
}
```

## 🚀 **启动脚本**

### **完整启动**
```bash
# 启动所有服务
./start-all-services.sh

# 检查服务状态
curl http://localhost:27007/health  # API网关
curl http://localhost:27001/health  # API服务
curl http://localhost:27002/health  # AI服务
```

### **停止所有服务**
```bash
# 停止所有服务
./stop-all-services.sh
```

### **单独启动服务**
```bash
# API服务
cd backend/api-service && npm run dev

# AI服务
cd backend/ai-service && python -m app.main

# 用户服务
cd user-service && npm run dev

# 媒体服务
cd media-service && npm run dev

# 新闻服务
cd news-service && npm run dev

# 赛程服务
cd schedule-service && npm run dev

# API网关
cd api-gateway && npm run dev

# 前端应用
cd frontpage && npm run dev
```

## 🔍 **端口检查命令**

### **检查端口占用**
```bash
# 检查所有相关端口
for port in 3000 27001 27002 27003 27004 27005 27006 27007 27008 27009; do
  echo "端口 $port:"
  lsof -i :$port
  echo "---"
done
```

### **释放端口**
```bash
# 杀死占用特定端口的进程
lsof -ti:端口号 | xargs kill -9

# 例如：释放27001端口
lsof -ti:27001 | xargs kill -9
```

## 🌍 **环境变量配置**

### **开发环境**
```env
# 所有服务使用localhost
API_SERVICE_URL=http://localhost:27001
AI_SERVICE_URL=http://localhost:27002
USER_SERVICE_URL=http://localhost:27003
MEDIA_SERVICE_URL=http://localhost:27004
NEWS_SERVICE_URL=http://localhost:27005
SCHEDULE_SERVICE_URL=http://localhost:27006
API_GATEWAY_URL=http://localhost:27007
```

### **生产环境**
```env
# 使用实际域名或IP
API_SERVICE_URL=http://api.fanzdnet.com:27001
AI_SERVICE_URL=http://ai.fanzdnet.com:27002
USER_SERVICE_URL=http://user.fanzdnet.com:27003
MEDIA_SERVICE_URL=http://media.fanzdnet.com:27004
NEWS_SERVICE_URL=http://news.fanzdnet.com:27005
SCHEDULE_SERVICE_URL=http://schedule.fanzdnet.com:27006
API_GATEWAY_URL=http://gateway.fanzdnet.com:27007
```

## 🔒 **安全配置**

### **防火墙规则**
```bash
# 开放必要端口
sudo ufw allow 3000    # 前端
sudo ufw allow 27001   # API服务
sudo ufw allow 27002   # AI服务
sudo ufw allow 27007   # API网关

# 内部服务端口仅允许本地访问
sudo ufw deny 27003    # 用户服务
sudo ufw deny 27004    # 媒体服务
sudo ufw deny 27005    # 新闻服务
sudo ufw deny 27006    # 赛程服务
```

### **Nginx反向代理配置**
```nginx
# /etc/nginx/sites-available/fanzdnet
server {
    listen 80;
    server_name fanzdnet.com;
    
    # 前端应用
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # API网关
    location /api/ {
        proxy_pass http://localhost:27007;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 📊 **监控配置**

### **健康检查端点**
- API服务: http://localhost:27001/health
- AI服务: http://localhost:27002/health
- 用户服务: http://localhost:27003/health
- 媒体服务: http://localhost:27004/health
- 新闻服务: http://localhost:27005/health
- 赛程服务: http://localhost:27006/health
- API网关: http://localhost:27007/health

### **监控指标端点**
- AI服务监控: http://localhost:27008/metrics
- API网关监控: http://localhost:27009/metrics

## 🐛 **故障排除**

### **常见问题**

1. **端口被占用**
   ```bash
   # 查看占用进程
   lsof -i :端口号
   
   # 杀死进程
   kill -9 PID
   ```

2. **服务无法启动**
   ```bash
   # 检查配置文件
   cat backend/api-service/.env
   
   # 检查日志
   tail -f logs/api-service.log
   ```

3. **服务间通信失败**
   ```bash
   # 测试连接
   curl http://localhost:27001/health
   curl http://localhost:27002/health
   ```

## 📝 **更新记录**

- **2024-12-29**: 完成端口重新分配
  - API服务: 3001 → 27001
  - AI服务: 8000 → 27002
  - 新增用户服务: 27003
  - 新增媒体服务: 27004
  - 新增新闻服务: 27005
  - 新增赛程服务: 27006
  - 新增API网关: 27007

---

**配置完成！所有服务端口已按照新方案重新分配并测试通过。** 🎉
