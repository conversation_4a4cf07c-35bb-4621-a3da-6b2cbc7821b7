# 依赖目录
node_modules/
__pycache__/
*.py[cod]
*$py.class
pip-log.txt
pip-delete-this-directory.txt

# Java相关
*.class
*.jar
*.war
*.ear
target/
.gradle/
build/
out/

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志文件
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 编辑器和IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# 缓存目录
.cache/
.pytest_cache/
.coverage
.nyc_output/

# 临时文件
tmp/
temp/
*.tmp
*.temp

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 媒体服务特定
# 原始媒体文件存储（通常应该在CDN或对象存储中）
uploads/
media-uploads/
raw-media/

# 处理后的媒体文件
processed/
thumbnails/
resized/
compressed/
optimized/

# 视频处理
video-processing/
transcoded/
video-thumbnails/
video-preview/

# 图片处理
image-processing/
watermarked/
cropped/
filtered/

# 音频处理
audio-processing/
audio-compressed/

# CDN缓存
cdn-cache/
edge-cache/

# 媒体转换临时文件
conversion-temp/
ffmpeg-temp/
imagemagick-temp/

# 媒体分析结果
analysis-results/
metadata-cache/
content-analysis/

# 大文件上传临时存储
chunk-uploads/
multipart-uploads/

# 备份和归档
backup-media/
archived-media/

# Docker相关
.dockerignore
Dockerfile.local

# SSL证书和密钥文件
*.pem
*.key
*.crt
*.p12
ssl/
certs/

# 配置文件
config/local.*
config/production.*
config/development.*

# 测试相关
test-media/
mock-files/
fixtures/

# 备份文件
*.bak
*.backup
*.old 