# 服务器配置
NODE_ENV=development
PORT=27004
HOST=localhost

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=netPA-2025
DB_NAME=fanzd_net

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=3

# 文件存储配置
STORAGE_PATH=../storage
UPLOAD_PATH=../storage/uploads
MEDIA_PATH=../storage/media
THUMBNAIL_PATH=../storage/thumbnails
TEMP_PATH=../storage/temp
MAX_FILE_SIZE=52428800

# 图像处理配置
MAX_IMAGE_WIDTH=4096
MAX_IMAGE_HEIGHT=4096
THUMBNAIL_SIZES=150,300,600
IMAGE_QUALITY=85
WEBP_QUALITY=80

# 视频处理配置
MAX_VIDEO_SIZE=104857600
VIDEO_FORMATS=mp4,webm,avi
THUMBNAIL_TIME=00:00:01

# CDN配置
CDN_ENABLED=false
CDN_BASE_URL=https://cdn.fanzdnet.com
CDN_ACCESS_KEY=your-cdn-access-key
CDN_SECRET_KEY=your-cdn-secret-key

# 安全配置
ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,webp,mp4,webm,avi
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=50

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/media-service.log

# CORS配置
CORS_ORIGIN=http://localhost:3000
CORS_CREDENTIALS=true

# 服务间通信
API_SERVICE_URL=http://localhost:27001
USER_SERVICE_URL=http://localhost:27003
