# 樊振东球迷网站AI应援物生成器 - 启动指南

## 🚀 快速启动

### 环境要求
- Node.js 18+
- Python 3.9+
- MySQL 8.0+
- Redis 6.0+
- Gemini API密钥

### 1. 数据库启动

```bash
# 启动MySQL
sudo systemctl start mysql  # Linux
brew services start mysql   # macOS

# 启动Redis
sudo systemctl start redis-server  # Linux
brew services start redis          # macOS

# 初始化数据库
mysql -u root -p < database/init_fanzd_net.sql
```

### 2. 后端API服务启动

```bash
cd backend/api-service

# 安装依赖
npm install

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，配置数据库连接

# 启动服务
npm run dev
# 服务地址: http://localhost:3001
```

### 3. AI服务启动

```bash
cd backend/ai-service

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate  # Linux/macOS

# 安装依赖
pip install -r requirements.txt

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，配置Gemini API密钥

# 启动服务
python -m app.main
# 服务地址: http://localhost:8000
```

### 4. 增强版前端启动

```bash
cd frontpage/ai-generator

# 安装依赖
npm install

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件

# 启动开发服务器
npm run dev
# 访问地址: http://localhost:3000
```

## 🎯 访问地址

- **前端应用**: http://localhost:3000
- **API网关**: http://localhost:27007
- **API服务**: http://localhost:27001
- **AI服务**: http://localhost:27002
- **用户服务**: http://localhost:27003
- **媒体服务**: http://localhost:27004
- **新闻服务**: http://localhost:27005
- **赛程服务**: http://localhost:27006
- **健康检查**:
  - API: http://localhost:27001/health
  - AI: http://localhost:27002/health
  - 网关: http://localhost:27007/health

## 🔧 环境配置

### API服务 (.env)
```env
NODE_ENV=development
PORT=27001
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=netPA-2025
DB_NAME=fanzd_net
JWT_SECRET=your-jwt-secret
REDIS_HOST=localhost
REDIS_PORT=6379
AI_SERVICE_URL=http://localhost:27002
```

### AI服务 (.env)
```env
HOST=0.0.0.0
PORT=27002
GEMINI_API_KEY=your-gemini-api-key
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=netPA-2025
DB_NAME=fanzd_net
REDIS_HOST=localhost
REDIS_PORT=6379
METRICS_PORT=27008
```

### API网关 (.env)
```env
NODE_ENV=development
PORT=27007
API_SERVICE_URL=http://localhost:27001
AI_SERVICE_URL=http://localhost:27002
USER_SERVICE_URL=http://localhost:27003
MEDIA_SERVICE_URL=http://localhost:27004
NEWS_SERVICE_URL=http://localhost:27005
SCHEDULE_SERVICE_URL=http://localhost:27006
REDIS_HOST=localhost
REDIS_PORT=6379
```

### 前端应用 (.env)
```env
VITE_API_BASE_URL=http://localhost:27007/api
VITE_AI_API_BASE_URL=http://localhost:27002/api/v1
VITE_API_GATEWAY_URL=http://localhost:27007
```

## 🧪 功能测试

### 1. 基础功能测试
```bash
# 测试API网关
curl http://localhost:27007/health

# 测试API服务
curl http://localhost:27001/health

# 测试AI服务
curl http://localhost:27002/health

# 测试用户服务
curl http://localhost:27003/health

# 测试媒体服务
curl http://localhost:27004/health

# 测试新闻服务
curl http://localhost:27005/health

# 测试赛程服务
curl http://localhost:27006/health

# 测试数据库连接
curl http://localhost:27001/api/auth/test
```

### 2. 增强功能测试
- 访问 http://localhost:3000
- 注册/登录账户
- 选择"应援横幅"
- 测试视觉元素选择器
- 测试实时预览功能
- 测试AI生成功能

## 🎨 新功能使用指南

### 视觉元素选择器
1. 在横幅生成器中找到"视觉元素"部分
2. 选择元素分类（人物、体育、荣誉等）
3. 点击元素卡片添加到设计中
4. 调整元素属性（位置、大小、透明度等）

### 实时预览
1. 支持Canvas和SVG两种预览模式
2. 实时显示文字和视觉元素组合效果
3. 可以导出预览图片

### AI增强生成
1. 输入创意描述
2. 选择视觉元素和参数
3. AI生成完整设计方案
4. 包含布局、颜色、特效建议

## 🐛 故障排除

### 常见问题

1. **数据库连接失败**
   ```bash
   # 检查MySQL状态
   sudo systemctl status mysql
   
   # 重启MySQL
   sudo systemctl restart mysql
   ```

2. **Redis连接失败**
   ```bash
   # 检查Redis状态
   redis-cli ping
   
   # 重启Redis
   sudo systemctl restart redis-server
   ```

3. **Gemini API错误**
   - 检查API密钥是否正确
   - 确认网络连接正常
   - 查看AI服务日志

4. **前端组件加载失败**
   ```bash
   # 清理缓存重新安装
   rm -rf node_modules package-lock.json
   npm install
   ```

### 日志查看

```bash
# API服务日志
cd backend/api-service
npm run dev  # 查看控制台输出

# AI服务日志
cd backend/ai-service
python -m app.main  # 查看控制台输出

# 前端开发日志
cd frontpage/ai-generator
npm run dev  # 查看控制台输出
```

## 📊 性能监控

### 服务状态检查
```bash
# 检查所有服务状态
curl http://localhost:3001/health
curl http://localhost:8000/health
curl http://localhost:8000/api/v1/models/status
```

### 数据库性能
```sql
-- 检查数据库连接
SHOW PROCESSLIST;

-- 检查表状态
SHOW TABLE STATUS FROM fanzd_net;
```

## 🔄 开发模式

### 热重载开发
所有服务都支持热重载：
- API服务：使用 `npm run dev`
- AI服务：使用 `python -m app.main` 
- 前端：使用 `npm run dev`

### 代码修改后自动重启
- 修改后端代码会自动重启服务
- 修改前端代码会自动刷新页面
- 修改AI服务代码需要手动重启

## 🎯 下一步

1. 完成基础功能测试
2. 测试增强版视觉元素功能
3. 验证AI生成效果
4. 根据需要调整参数和配置
5. 部署到生产环境

---

**技术支持**: 如有问题请查看日志或联系开发团队
