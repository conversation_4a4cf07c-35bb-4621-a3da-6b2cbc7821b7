{"name": "fanzd-api-gateway", "version": "1.0.0", "description": "樊振东球迷网站API网关", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["fanzhendong", "api-gateway", "microservices", "express", "proxy"], "author": "FanZD Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "dotenv": "^16.3.1", "redis": "^4.6.10", "http-proxy-middleware": "^2.0.6", "express-rate-limit": "^7.1.5", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "winston": "^3.11.0", "uuid": "^9.0.1", "axios": "^1.6.2", "consul": "^0.40.0", "prom-client": "^15.0.0", "jaeger-client": "^3.19.0", "circuit-breaker-js": "^0.0.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0"}, "engines": {"node": ">=18.0.0"}}