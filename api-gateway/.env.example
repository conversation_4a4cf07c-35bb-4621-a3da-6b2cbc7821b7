# 服务器配置
NODE_ENV=development
PORT=27007
HOST=localhost

# 服务发现配置
SERVICE_DISCOVERY=static
CONSUL_HOST=localhost
CONSUL_PORT=8500

# 后端服务配置
API_SERVICE_URL=http://localhost:27001
AI_SERVICE_URL=http://localhost:27002
USER_SERVICE_URL=http://localhost:27003
MEDIA_SERVICE_URL=http://localhost:27004
NEWS_SERVICE_URL=http://localhost:27005
SCHEDULE_SERVICE_URL=http://localhost:27006

# 负载均衡配置
LOAD_BALANCER=round_robin
HEALTH_CHECK_INTERVAL=30000
CIRCUIT_BREAKER_ENABLED=true
CIRCUIT_BREAKER_THRESHOLD=5

# 缓存配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=6
CACHE_TTL=300

# 限流配置
RATE_LIMIT_ENABLED=true
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000
RATE_LIMIT_SKIP_SUCCESSFUL=false

# 认证配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
AUTH_WHITELIST=/health,/metrics,/api/auth/login,/api/auth/register

# 监控配置
METRICS_ENABLED=true
METRICS_PORT=27009
PROMETHEUS_ENABLED=true
JAEGER_ENABLED=false

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/api-gateway.log
ACCESS_LOG_ENABLED=true

# CORS配置
CORS_ORIGIN=http://localhost:3000
CORS_CREDENTIALS=true
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_HEADERS=Content-Type,Authorization,X-Requested-With

# SSL配置
SSL_ENABLED=false
SSL_CERT_PATH=certs/server.crt
SSL_KEY_PATH=certs/server.key

# 安全配置
HELMET_ENABLED=true
COMPRESSION_ENABLED=true
TRUST_PROXY=false
