# 操作系统生成的文件
# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
Icon?

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp

# Linux
*~

# 编辑器和IDE
.vscode/
.idea/
.cursor/
*.swp
*.swo
*~

# 日志文件
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 临时文件
tmp/
temp/

# 备份文件
*.bak
*.backup
*.old

# 压缩文件
*.zip
*.tar.gz
*.rar
*.7z

# 证书和密钥文件
*.pem
*.key
*.crt
*.p12
ssl/
certs/

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 缓存目录
.cache/

# 构建和部署相关
build/
dist/
deployment/

# Docker相关
.dockerignore
docker-compose.override.yml

# 依赖目录（各子项目会有自己的.gitignore）
node_modules/

# 本地配置文件
config/local.*
*.local 