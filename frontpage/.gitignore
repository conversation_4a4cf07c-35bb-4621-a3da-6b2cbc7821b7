# 依赖目录
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 构建输出目录
dist/
dist-ssr/
*.local

# 编辑器和IDE
.vscode/*
!.vscode/extensions.json
.idea/
*.swp
*.swo
*~

# 操作系统生成的文件
# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Linux
*~

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志文件
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 运行时数据
pids/
*.pid
*.seed
*.pid.lock

# Coverage目录（测试覆盖率）
coverage/
*.lcov
.nyc_output/

# 缓存目录
.cache/
.parcel-cache/
.vite/

# 临时文件
tmp/
temp/

# 可选的npm缓存目录
.npm

# 可选的REPL历史
.node_repl_history

# 输出的二进制文件
*.tgz

# Yarn完整性文件
.yarn-integrity

# dotenv环境变量文件
.env.test

# parcel-bundler缓存
.parcel-cache

# Next.js构建输出
.next

# Nuxt.js构建/生成输出
.nuxt
dist

# Gatsby文件
.cache/
public

# Storybook构建输出
.out
.storybook-out

# Rollup.js默认构建输出
/dist/

# Serverless目录
.serverless/

# FuseBox缓存
.fusebox/

# DynamoDB本地文件
.dynamodb/

# TernJS端口文件
.tern-port

# Stores VSCode版本用于远程容器
.vscode-server

# SvelteKit构建输出
.svelte-kit

# Vite
vite.config.js.timestamp-*
vite.config.ts.timestamp-*

# 自定义忽略文件
# 用户上传的文件
uploads/
user-uploads/

# 备份文件
*.bak
*.backup
*.old

# 压缩文件
*.zip
*.tar.gz
*.rar

# 证书和密钥文件
*.pem
*.key
*.crt
*.p12

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 本地配置文件
config/local.js
config/local.json 