/**
 * AI生成器状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

export const useGeneratorStore = defineStore('generator', () => {
  // 状态
  const isGenerating = ref(false)
  const currentTask = ref(null)
  const generationHistory = ref([])
  const apiBaseUrl = ref('http://localhost:8000/api/v1')
  
  // 计算属性
  const hasHistory = computed(() => generationHistory.value.length > 0)
  const latestGeneration = computed(() => 
    generationHistory.value[generationHistory.value.length - 1]
  )
  
  // 生成内容
  const generateContent = async (params) => {
    try {
      isGenerating.value = true
      
      const taskId = generateTaskId()
      currentTask.value = {
        id: taskId,
        ...params,
        status: 'pending',
        progress: 0,
        createdAt: new Date()
      }
      
      // 调用AI服务生成
      const response = await fetch(`${apiBaseUrl.value}/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          task_id: taskId,
          type: params.type,
          prompt: params.prompt,
          parameters: params.parameters
        })
      })
      
      if (!response.ok) {
        throw new Error(`生成请求失败: ${response.statusText}`)
      }
      
      const result = await response.json()
      
      // 轮询任务状态
      const finalResult = await pollTaskStatus(taskId)
      
      // 添加到历史记录
      const generationRecord = {
        id: taskId,
        type: params.type,
        prompt: params.prompt,
        parameters: params.parameters,
        result: finalResult,
        createdAt: new Date(),
        status: 'completed'
      }
      
      generationHistory.value.push(generationRecord)
      
      return finalResult
      
    } catch (error) {
      console.error('生成失败:', error)
      
      if (currentTask.value) {
        currentTask.value.status = 'failed'
        currentTask.value.error = error.message
      }
      
      throw error
    } finally {
      isGenerating.value = false
      currentTask.value = null
    }
  }
  
  // 轮询任务状态
  const pollTaskStatus = async (taskId, maxAttempts = 60) => {
    let attempts = 0
    
    while (attempts < maxAttempts) {
      try {
        const response = await fetch(`${apiBaseUrl.value}/task/${taskId}`)
        
        if (!response.ok) {
          throw new Error(`获取任务状态失败: ${response.statusText}`)
        }
        
        const taskStatus = await response.json()
        
        // 更新当前任务状态
        if (currentTask.value && currentTask.value.id === taskId) {
          currentTask.value.status = taskStatus.status
          currentTask.value.progress = taskStatus.progress
        }
        
        if (taskStatus.status === 'completed') {
          return taskStatus.result
        }
        
        if (taskStatus.status === 'failed') {
          throw new Error(taskStatus.error || '生成失败')
        }
        
        // 等待2秒后重试
        await new Promise(resolve => setTimeout(resolve, 2000))
        attempts++
        
      } catch (error) {
        console.error('轮询任务状态失败:', error)
        attempts++
        
        if (attempts >= maxAttempts) {
          throw new Error('任务超时，请重试')
        }
        
        await new Promise(resolve => setTimeout(resolve, 2000))
      }
    }
    
    throw new Error('任务超时，请重试')
  }
  
  // 获取任务状态
  const getTaskStatus = async (taskId) => {
    try {
      const response = await fetch(`${apiBaseUrl.value}/task/${taskId}`)
      
      if (!response.ok) {
        throw new Error(`获取任务状态失败: ${response.statusText}`)
      }
      
      return await response.json()
    } catch (error) {
      console.error('获取任务状态失败:', error)
      throw error
    }
  }
  
  // 下载结果
  const downloadResult = (result) => {
    try {
      if (result.type === 'banner' || result.type === 'emoji') {
        // 图片下载
        if (result.image_path) {
          const link = document.createElement('a')
          link.href = `http://localhost:8000/${result.image_path}`
          link.download = `樊振东${result.type}_${Date.now()}.png`
          link.click()
        }
      } else if (result.type === 'slogan') {
        // 文本下载
        const content = result.slogans
          .map((slogan, index) => `${index + 1}. ${slogan.text}`)
          .join('\n')
        
        const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
        const link = document.createElement('a')
        link.href = URL.createObjectURL(blob)
        link.download = `樊振东应援口号_${Date.now()}.txt`
        link.click()
        URL.revokeObjectURL(link.href)
      }
      
      ElMessage.success('下载成功')
    } catch (error) {
      console.error('下载失败:', error)
      ElMessage.error('下载失败')
    }
  }
  
  // 清除历史记录
  const clearHistory = () => {
    generationHistory.value = []
    ElMessage.success('历史记录已清除')
  }
  
  // 删除历史记录项
  const removeHistoryItem = (id) => {
    const index = generationHistory.value.findIndex(item => item.id === id)
    if (index > -1) {
      generationHistory.value.splice(index, 1)
      ElMessage.success('记录已删除')
    }
  }
  
  // 获取生成统计
  const getGenerationStats = () => {
    const stats = {
      total: generationHistory.value.length,
      byType: {},
      recent: generationHistory.value.slice(-10)
    }
    
    generationHistory.value.forEach(item => {
      stats.byType[item.type] = (stats.byType[item.type] || 0) + 1
    })
    
    return stats
  }
  
  // 生成任务ID
  const generateTaskId = () => {
    return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
  
  // 检查AI服务状态
  const checkServiceStatus = async () => {
    try {
      const response = await fetch(`${apiBaseUrl.value}/health`)
      
      if (!response.ok) {
        throw new Error('AI服务不可用')
      }
      
      const status = await response.json()
      return status
    } catch (error) {
      console.error('检查服务状态失败:', error)
      throw error
    }
  }
  
  // 获取模型状态
  const getModelsStatus = async () => {
    try {
      const response = await fetch(`${apiBaseUrl.value}/models/status`)
      
      if (!response.ok) {
        throw new Error('获取模型状态失败')
      }
      
      return await response.json()
    } catch (error) {
      console.error('获取模型状态失败:', error)
      throw error
    }
  }
  
  return {
    // 状态
    isGenerating,
    currentTask,
    generationHistory,
    apiBaseUrl,
    
    // 计算属性
    hasHistory,
    latestGeneration,
    
    // 方法
    generateContent,
    pollTaskStatus,
    getTaskStatus,
    downloadResult,
    clearHistory,
    removeHistoryItem,
    getGenerationStats,
    checkServiceStatus,
    getModelsStatus
  }
})
