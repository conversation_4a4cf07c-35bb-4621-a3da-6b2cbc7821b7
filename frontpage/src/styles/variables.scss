// 设计系统变量定义

// ===== 颜色系统 =====
// 主色调 - 蓝色系
$primary-color: #409EFF;
$primary-light-1: #53a8ff;
$primary-light-2: #66b1ff;
$primary-light-3: #79bbff;
$primary-light-4: #8cc5ff;
$primary-light-5: #a0cfff;
$primary-light-6: #b3d8ff;
$primary-light-7: #c6e2ff;
$primary-light-8: #d9ecff;
$primary-light-9: #ecf5ff;
$primary-dark-1: #337ecc;
$primary-dark-2: #2d70b3;

// 背景色 - 白色系
$bg-color: #FFFFFF;
$bg-color-page: #F8F9FA;
$bg-color-overlay: #FFFFFF;

// 文字颜色
$text-color-primary: #303133;
$text-color-regular: #606266;
$text-color-secondary: #909399;
$text-color-placeholder: #C0C4CC;
$text-color-disabled: #C0C4CC;

// 边框颜色
$border-color: #DCDFE6;
$border-color-light: #E4E7ED;
$border-color-lighter: #EBEEF5;
$border-color-extra-light: #F2F6FC;
$border-color-dark: #D4D7DE;
$border-color-darker: #CDD0D6;

// 功能色
$success-color: #67C23A;
$warning-color: #E6A23C;
$danger-color: #F56C6C;
$info-color: #909399;

$success-light-1: #85ce61;
$success-light-2: #95d475;
$success-light-3: #a4da89;
$success-light-4: #b3e19d;
$success-light-5: #c2e7b0;
$success-light-6: #d1edc4;
$success-light-7: #e1f3d8;
$success-light-8: #f0f9eb;
$success-light-9: #f0f9eb;

$warning-light-1: #ebb563;
$warning-light-2: #f0c78a;
$warning-light-3: #f5d9b1;
$warning-light-4: #faecd8;
$warning-light-5: #fdf6ec;

$danger-light-1: #f78989;
$danger-light-2: #f9a7a7;
$danger-light-3: #fbc4c4;
$danger-light-4: #fde2e2;
$danger-light-5: #fef0f0;

// ===== 字体系统 =====
$font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
$font-family-mono: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;

// 字体大小
$font-size-extra-large: 20px;
$font-size-large: 18px;
$font-size-medium: 16px;
$font-size-base: 14px;
$font-size-small: 13px;
$font-size-extra-small: 12px;

// 字体粗细
$font-weight-primary: 500;
$font-weight-bold: 700;

// 行高
$line-height-primary: 1.5;
$line-height-base: 1.4;

// ===== 间距系统 =====
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 12px;
$spacing-lg: 16px;
$spacing-xl: 20px;
$spacing-xxl: 24px;
$spacing-xxxl: 32px;

// ===== 圆角系统 =====
$border-radius-base: 4px;
$border-radius-small: 2px;
$border-radius-round: 20px;
$border-radius-circle: 100%;

// ===== 阴影系统 =====
$box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
$box-shadow-dark: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.12);
$box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

// ===== 动画系统 =====
$transition-duration: 0.3s;
$transition-function-ease-in-out-bezier: cubic-bezier(0.645, 0.045, 0.355, 1);
$transition-function-fast-bezier: cubic-bezier(0.23, 1, 0.32, 1);
$transition-all: all $transition-duration $transition-function-ease-in-out-bezier;
$transition-fade: opacity $transition-duration $transition-function-fast-bezier;
$transition-md-fade: transform $transition-duration $transition-function-fast-bezier, opacity $transition-duration $transition-function-fast-bezier;
$transition-border: border-color $transition-duration $transition-function-ease-in-out-bezier;
$transition-box-shadow: box-shadow $transition-duration $transition-function-ease-in-out-bezier;
$transition-color: color $transition-duration $transition-function-ease-in-out-bezier;

// ===== 断点系统 =====
$breakpoint-xs: 0;
$breakpoint-sm: 576px;
$breakpoint-md: 768px;
$breakpoint-lg: 992px;
$breakpoint-xl: 1200px;
$breakpoint-xxl: 1400px;

// ===== Z-index 层级 =====
$z-index-normal: 1;
$z-index-top: 1000;
$z-index-popper: 2000;
$z-index-dialog: 2001;
$z-index-message: 3000;
$z-index-loading: 4000;

// ===== 组件特定变量 =====
// Header
$header-height: 64px;
$header-bg: $bg-color;
$header-shadow: $box-shadow-light;

// Footer
$footer-bg: #F8F9FA;
$footer-border: $border-color-light;

// Card
$card-border-radius: 8px;
$card-shadow: $box-shadow-light;
$card-padding: $spacing-xl;

// Button
$button-border-radius: $border-radius-base;
$button-padding-vertical: 8px;
$button-padding-horizontal: 16px;

// Input
$input-border-radius: $border-radius-base;
$input-border-color: $border-color;
$input-border-color-hover: $border-color-dark;
$input-border-color-focus: $primary-color;

// ===== 主题色彩映射 =====
:export {
  primaryColor: $primary-color;
  bgColor: $bg-color;
  bgColorPage: $bg-color-page;
  textColorPrimary: $text-color-primary;
  textColorRegular: $text-color-regular;
  borderColor: $border-color;
  successColor: $success-color;
  warningColor: $warning-color;
  dangerColor: $danger-color;
  infoColor: $info-color;
}
