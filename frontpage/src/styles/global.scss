// 全局样式定义
@use './variables.scss' as *;

// ===== 全局重置样式 =====
* {
  box-sizing: border-box;
}

html {
  font-size: 14px;
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;

  // 滚动性能优化
  scroll-behavior: smooth;
  // 启用硬件加速
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

body {
  margin: 0;
  font-family: $font-family;
  font-size: $font-size-base;
  font-weight: 400;
  line-height: $line-height-primary;
  color: $text-color-primary;
  background-color: $bg-color-page;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  // 滚动性能优化
  -webkit-overflow-scrolling: touch;
  // 减少重绘
  will-change: auto; // 避免过度使用will-change
}

// ===== 链接样式 =====
a {
  color: $primary-color;
  text-decoration: none;
  background-color: transparent;
  outline: none;
  cursor: pointer;
  transition: $transition-color;

  &:hover {
    color: $primary-light-1;
  }

  &:active {
    color: $primary-dark-1;
  }

  &:focus {
    outline: thin dotted;
    outline-offset: -2px;
  }
}

// ===== 标题样式 =====
h1, h2, h3, h4, h5, h6 {
  margin: 0 0 $spacing-lg 0;
  font-weight: $font-weight-primary;
  line-height: $line-height-base;
  color: $text-color-primary;
}

h1 {
  font-size: $font-size-extra-large * 1.6;
}

h2 {
  font-size: $font-size-extra-large * 1.4;
}

h3 {
  font-size: $font-size-extra-large * 1.2;
}

h4 {
  font-size: $font-size-extra-large;
}

h5 {
  font-size: $font-size-large;
}

h6 {
  font-size: $font-size-medium;
}

// ===== 段落和文本样式 =====
p {
  margin: 0 0 $spacing-lg 0;
  line-height: $line-height-primary;
}

small {
  font-size: $font-size-small;
  color: $text-color-secondary;
}

strong, b {
  font-weight: $font-weight-bold;
}

// ===== 列表样式 =====
ul, ol {
  margin: 0 0 $spacing-lg 0;
  padding-left: $spacing-xl;
}

li {
  margin-bottom: $spacing-xs;
}

// ===== 表格样式 =====
table {
  border-collapse: collapse;
  border-spacing: 0;
  width: 100%;
}

th, td {
  padding: $spacing-md;
  text-align: left;
  border-bottom: 1px solid $border-color-light;
}

th {
  font-weight: $font-weight-primary;
  color: $text-color-primary;
  background-color: $bg-color-page;
}

// ===== 表单元素样式 =====
input, textarea, select, button {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

// ===== 图片样式 =====
img {
  max-width: 100%;
  height: auto;
  vertical-align: middle;
  border-style: none;
}

// ===== 代码样式 =====
code, pre {
  font-family: $font-family-mono;
  font-size: $font-size-small;
}

code {
  padding: 2px 4px;
  background-color: $bg-color-page;
  border-radius: $border-radius-small;
  color: $danger-color;
}

pre {
  padding: $spacing-lg;
  background-color: $bg-color-page;
  border-radius: $border-radius-base;
  overflow-x: auto;
  line-height: $line-height-base;
}

// ===== 工具类 =====
// 文本对齐
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }
.text-justify { text-align: justify !important; }

// 文本颜色
.text-primary { color: $text-color-primary !important; }
.text-regular { color: $text-color-regular !important; }
.text-secondary { color: $text-color-secondary !important; }
.text-placeholder { color: $text-color-placeholder !important; }
.text-success { color: $success-color !important; }
.text-warning { color: $warning-color !important; }
.text-danger { color: $danger-color !important; }
.text-info { color: $info-color !important; }

// 背景颜色
.bg-primary { background-color: $primary-color !important; }
.bg-success { background-color: $success-color !important; }
.bg-warning { background-color: $warning-color !important; }
.bg-danger { background-color: $danger-color !important; }
.bg-info { background-color: $info-color !important; }
.bg-white { background-color: $bg-color !important; }
.bg-light { background-color: $bg-color-page !important; }

// 间距
.m-0 { margin: 0 !important; }
.mt-0 { margin-top: 0 !important; }
.mr-0 { margin-right: 0 !important; }
.mb-0 { margin-bottom: 0 !important; }
.ml-0 { margin-left: 0 !important; }

.m-1 { margin: $spacing-xs !important; }
.mt-1 { margin-top: $spacing-xs !important; }
.mr-1 { margin-right: $spacing-xs !important; }
.mb-1 { margin-bottom: $spacing-xs !important; }
.ml-1 { margin-left: $spacing-xs !important; }

.m-2 { margin: $spacing-sm !important; }
.mt-2 { margin-top: $spacing-sm !important; }
.mr-2 { margin-right: $spacing-sm !important; }
.mb-2 { margin-bottom: $spacing-sm !important; }
.ml-2 { margin-left: $spacing-sm !important; }

.m-3 { margin: $spacing-md !important; }
.mt-3 { margin-top: $spacing-md !important; }
.mr-3 { margin-right: $spacing-md !important; }
.mb-3 { margin-bottom: $spacing-md !important; }
.ml-3 { margin-left: $spacing-md !important; }

.m-4 { margin: $spacing-lg !important; }
.mt-4 { margin-top: $spacing-lg !important; }
.mr-4 { margin-right: $spacing-lg !important; }
.mb-4 { margin-bottom: $spacing-lg !important; }
.ml-4 { margin-left: $spacing-lg !important; }

.m-5 { margin: $spacing-xl !important; }
.mt-5 { margin-top: $spacing-xl !important; }
.mr-5 { margin-right: $spacing-xl !important; }
.mb-5 { margin-bottom: $spacing-xl !important; }
.ml-5 { margin-left: $spacing-xl !important; }

// 内边距
.p-0 { padding: 0 !important; }
.pt-0 { padding-top: 0 !important; }
.pr-0 { padding-right: 0 !important; }
.pb-0 { padding-bottom: 0 !important; }
.pl-0 { padding-left: 0 !important; }

.p-1 { padding: $spacing-xs !important; }
.pt-1 { padding-top: $spacing-xs !important; }
.pr-1 { padding-right: $spacing-xs !important; }
.pb-1 { padding-bottom: $spacing-xs !important; }
.pl-1 { padding-left: $spacing-xs !important; }

.p-2 { padding: $spacing-sm !important; }
.pt-2 { padding-top: $spacing-sm !important; }
.pr-2 { padding-right: $spacing-sm !important; }
.pb-2 { padding-bottom: $spacing-sm !important; }
.pl-2 { padding-left: $spacing-sm !important; }

.p-3 { padding: $spacing-md !important; }
.pt-3 { padding-top: $spacing-md !important; }
.pr-3 { padding-right: $spacing-md !important; }
.pb-3 { padding-bottom: $spacing-md !important; }
.pl-3 { padding-left: $spacing-md !important; }

.p-4 { padding: $spacing-lg !important; }
.pt-4 { padding-top: $spacing-lg !important; }
.pr-4 { padding-right: $spacing-lg !important; }
.pb-4 { padding-bottom: $spacing-lg !important; }
.pl-4 { padding-left: $spacing-lg !important; }

.p-5 { padding: $spacing-xl !important; }
.pt-5 { padding-top: $spacing-xl !important; }
.pr-5 { padding-right: $spacing-xl !important; }
.pb-5 { padding-bottom: $spacing-xl !important; }
.pl-5 { padding-left: $spacing-xl !important; }

// 显示/隐藏
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }

// Flex 布局
.flex-row { flex-direction: row !important; }
.flex-column { flex-direction: column !important; }
.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }
.justify-content-start { justify-content: flex-start !important; }
.justify-content-end { justify-content: flex-end !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-between { justify-content: space-between !important; }
.justify-content-around { justify-content: space-around !important; }
.align-items-start { align-items: flex-start !important; }
.align-items-end { align-items: flex-end !important; }
.align-items-center { align-items: center !important; }
.align-items-baseline { align-items: baseline !important; }
.align-items-stretch { align-items: stretch !important; }

// 位置
.position-static { position: static !important; }
.position-relative { position: relative !important; }
.position-absolute { position: absolute !important; }
.position-fixed { position: fixed !important; }
.position-sticky { position: sticky !important; }

// 宽度和高度
.w-25 { width: 25% !important; }
.w-50 { width: 50% !important; }
.w-75 { width: 75% !important; }
.w-100 { width: 100% !important; }
.h-25 { height: 25% !important; }
.h-50 { height: 50% !important; }
.h-75 { height: 75% !important; }
.h-100 { height: 100% !important; }

// 圆角
.rounded { border-radius: $border-radius-base !important; }
.rounded-sm { border-radius: $border-radius-small !important; }
.rounded-lg { border-radius: $border-radius-base * 2 !important; }
.rounded-circle { border-radius: $border-radius-circle !important; }
.rounded-0 { border-radius: 0 !important; }

// 阴影
.shadow-sm { box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important; }
.shadow { box-shadow: $box-shadow-base !important; }
.shadow-lg { box-shadow: $box-shadow-light !important; }
.shadow-none { box-shadow: none !important; }

// 溢出
.overflow-auto { overflow: auto !important; }
.overflow-hidden { overflow: hidden !important; }
.overflow-visible { overflow: visible !important; }
.overflow-scroll { overflow: scroll !important; }

// 用户选择
.user-select-all { user-select: all !important; }
.user-select-auto { user-select: auto !important; }
.user-select-none { user-select: none !important; }

// 指针事件
.pe-none { pointer-events: none !important; }
.pe-auto { pointer-events: auto !important; }

// ===== 响应式工具类 =====
@media (max-width: $breakpoint-sm - 1px) {
  .d-xs-none { display: none !important; }
  .d-xs-block { display: block !important; }
  .d-xs-flex { display: flex !important; }
}

@media (min-width: $breakpoint-sm) and (max-width: $breakpoint-md - 1px) {
  .d-sm-none { display: none !important; }
  .d-sm-block { display: block !important; }
  .d-sm-flex { display: flex !important; }
}

@media (min-width: $breakpoint-md) and (max-width: $breakpoint-lg - 1px) {
  .d-md-none { display: none !important; }
  .d-md-block { display: block !important; }
  .d-md-flex { display: flex !important; }
}

@media (min-width: $breakpoint-lg) {
  .d-lg-none { display: none !important; }
  .d-lg-block { display: block !important; }
  .d-lg-flex { display: flex !important; }
}
