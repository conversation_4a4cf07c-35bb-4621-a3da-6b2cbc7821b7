@import './base.css';

#app {
  /* 移除 max-width 限制以支持全屏轮播 */
  /* max-width: 1280px; */
  /* margin: 0 auto; */
  padding: 0; /* 移除默认 padding */
  font-weight: normal;
  width: 100%;
  min-height: 100vh;
}

a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
  padding: 3px;
}

@media (hover: hover) {
  a:hover {
    background-color: hsla(160, 100%, 37%, 0.2);
  }
}

/* 移除可能影响全屏布局的样式 - 已注释掉以支持全屏轮播 */
/*
@media (min-width: 1024px) {
  body {
    display: flex;
    place-items: center;
  }

  #app {
    display: grid;
    grid-template-columns: 1fr 1fr;
    padding: 0 2rem;
  }
}
*/
