{"name": "vue", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.10.0", "element-plus": "^2.10.2", "pinia": "^3.0.3", "sass": "^1.89.2", "vue": "^3.4.29", "vue-i18n": "^9.14.4", "vue-router": "^4.5.1"}, "devDependencies": {"@stagewise-plugins/vue": "^0.4.6", "@stagewise/toolbar-vue": "^0.4.8", "@vitejs/plugin-vue": "^5.0.5", "vite": "^5.3.1"}}