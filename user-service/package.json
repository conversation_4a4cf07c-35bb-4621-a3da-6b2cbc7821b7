{"name": "fanzd-user-service", "version": "1.0.0", "description": "樊振东球迷网站用户服务", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["fanzhendong", "user-service", "api", "express", "mysql"], "author": "FanZD Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "mysql2": "^3.6.5", "redis": "^4.6.10", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "express-validator": "^7.0.1", "express-rate-limit": "^7.1.5", "multer": "^1.4.5-lts.1", "sharp": "^0.33.5", "nodemailer": "^6.9.7", "winston": "^3.11.0", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0"}, "engines": {"node": ">=18.0.0"}}