# 服务器配置
NODE_ENV=development
PORT=27003
HOST=localhost

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=netPA-2025
DB_NAME=fanzd_net

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=2

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# 邮件配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-email-password
EMAIL_FROM=<EMAIL>

# 文件存储配置
UPLOAD_DIR=../storage/uploads
AVATAR_DIR=../storage/avatars
MAX_FILE_SIZE=5242880

# 安全配置
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/user-service.log

# CORS配置
CORS_ORIGIN=http://localhost:3000
CORS_CREDENTIALS=true

# 服务间通信
API_SERVICE_URL=http://localhost:27001
AI_SERVICE_URL=http://localhost:27002
