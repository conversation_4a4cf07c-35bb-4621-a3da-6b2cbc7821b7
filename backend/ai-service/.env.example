# 服务配置
HOST=0.0.0.0
PORT=8000
DEBUG=True
LOG_LEVEL=INFO

# AI服务配置
GEMINI_API_KEY=your-gemini-api-key-here
OPENAI_API_KEY=your-openai-api-key-here
AI_REQUEST_TIMEOUT=300
MAX_CONCURRENT_REQUESTS=10

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=1

# MySQL配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=netPA-2025
DB_NAME=fanzd_net

# 文件存储配置
STORAGE_PATH=../../storage
UPLOAD_PATH=../../storage/uploads
GENERATED_PATH=../../storage/generated
TEMP_PATH=../../storage/temp
MAX_FILE_SIZE=10485760

# 图像处理配置
MAX_IMAGE_WIDTH=2048
MAX_IMAGE_HEIGHT=2048
THUMBNAIL_SIZE=300
IMAGE_QUALITY=85

# 安全配置
SECRET_KEY=your-secret-key-change-this-in-production
ALLOWED_HOSTS=localhost,127.0.0.1

# 监控配置
ENABLE_METRICS=True
METRICS_PORT=8001
