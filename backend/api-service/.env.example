# 服务器配置
NODE_ENV=development
PORT=27001
HOST=localhost





# 静态资源配置
STATIC_DIR=../../storage/static
GENERATED_DIR=../../storage/generated
TEMP_DIR=../../storage/temp

# AI服务配置
AI_SERVICE_URL=http://localhost:27002
GEMINI_API_KEY=your-gemini-api-key
AI_REQUEST_TIMEOUT=300000

# 安全配置
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log

# CORS配置
CORS_ORIGIN=http://localhost:3000
CORS_CREDENTIALS=true

# Socket.IO配置
SOCKET_CORS_ORIGIN=http://localhost:3000
