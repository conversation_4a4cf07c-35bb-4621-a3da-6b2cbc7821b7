# 服务器配置
NODE_ENV=development
PORT=27001
HOST=localhost

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=netPA-2025
DB_NAME=fanzd_net

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# 文件存储配置
UPLOAD_DIR=../../storage/uploads
GENERATED_DIR=../../storage/generated
TEMP_DIR=../../storage/temp
MAX_FILE_SIZE=10485760

# AI服务配置
AI_SERVICE_URL=http://localhost:27002
GEMINI_API_KEY=your-gemini-api-key
AI_REQUEST_TIMEOUT=300000

# 安全配置
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log

# CORS配置
CORS_ORIGIN=http://localhost:3000
CORS_CREDENTIALS=true

# Socket.IO配置
SOCKET_CORS_ORIGIN=http://localhost:3000
