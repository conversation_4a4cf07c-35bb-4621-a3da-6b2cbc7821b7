{"name": "fanzd-api-service", "version": "1.0.0", "description": "樊振东球迷网站AI应援物生成器API服务", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["fanzhendong", "ai-generator", "api", "express", "mysql"], "author": "FanZD Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "mysql2": "^3.6.5", "redis": "^4.6.10", "bull": "^4.12.2", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "multer": "^1.4.5-lts.1", "sharp": "^0.33.1", "uuid": "^9.0.1", "axios": "^1.6.2", "dotenv": "^16.3.1", "joi": "^17.11.0", "winston": "^3.11.0", "socket.io": "^4.7.4"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1"}, "engines": {"node": ">=18.0.0"}}